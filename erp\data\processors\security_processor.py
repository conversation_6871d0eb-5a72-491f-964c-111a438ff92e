"""
Security Processor for ERP system

Specialized processor for handling security-related records like access rules,
record rules, and group permissions.
"""

from typing import Dict, List, Any, Optional

from .base import BaseDataProcessor
from ..sql_helpers import SQLHelpers, ModelSQLHelpers
from ..xmlid_manager import XMLIDManager


class SecurityProcessor(BaseDataProcessor):
    """
    Processor for security records (ir.model.access, ir.rule, etc.)
    
    Handles creation and updating of security-related records with proper
    validation and reference resolution.
    """
    
    def __init__(self, db_manager, name: str = "SecurityProcessor"):
        super().__init__(db_manager, name)
        
        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_manager = XMLIDManager(db_manager)
        
        # Supported security models
        self.supported_security_models = {
            'ir.model.access',
            'ir.rule',
            'res.groups',
            'res.users'
        }
    
    def can_process(self, item: Dict[str, Any]) -> bool:
        """Check if this processor can handle the given item"""
        if not isinstance(item, dict):
            return False
        
        model = item.get('model')
        return model in self.supported_security_models
    
    def get_supported_models(self) -> List[str]:
        """Get list of models this processor supports"""
        return list(self.supported_security_models)
    
    def get_processing_order(self) -> int:
        """Get processing order (process security early)"""
        return 10
    
    async def _process_item(self, item: Dict[str, Any], **kwargs) -> bool:
        """Process a single security item"""
        model = item['model']
        xml_id = item.get('xml_id')
        values = item.get('values', {})
        noupdate = item.get('noupdate', False)

        self.logger.info(f"🔄 Processing security item: {xml_id} (model: {model})")
        self.logger.debug(f"   Values: {values}")

        try:
            # Add timeout to prevent hanging on individual items
            import asyncio
            result = await asyncio.wait_for(
                self._process_item_internal(item, model, xml_id, values, noupdate),
                timeout=30.0  # 30 second timeout per item
            )
            self.logger.info(f"✅ Completed processing security item: {xml_id}")
            return result
        except asyncio.TimeoutError:
            error_msg = f"⏰ Timeout processing security item {xml_id} after 30 seconds"
            self.logger.error(error_msg)
            self.result.add_error(error_msg)
            return False
        except Exception as e:
            error_msg = f"❌ Error processing security item {xml_id}: {e}"
            self.logger.error(error_msg)
            self.result.add_error(error_msg)
            return False

    async def _process_item_internal(self, item: Dict[str, Any], model: str, xml_id: str, values: Dict[str, Any], noupdate: bool) -> bool:
        """Internal method for processing security item"""
        self.logger.debug(f"🔍 Step 1: Validating security data for {xml_id}")

        # Validate security data
        validation_result = await self._validate_security_data(values, model)
        if not validation_result['valid']:
            for error in validation_result['errors']:
                self.result.add_error(f"Security validation error for {xml_id}: {error}")
            return False

        self.logger.debug(f"🔍 Step 2: Processing security values for {xml_id}")

        # Process security-specific fields
        processed_values = await self._process_security_values(values, model)
        if processed_values is None:
            self.logger.error(f"❌ Failed to process security values for {xml_id}")
            return False

        self.logger.debug(f"🔍 Step 3: Checking if record exists for {xml_id}")

        # Check if record exists
        existing_record_id = None
        if xml_id:
            self.logger.debug(f"   Looking up existing record by XML ID: {xml_id}")
            existing_record_id = await self._find_record_by_xmlid(xml_id)
            if existing_record_id:
                self.logger.debug(f"   Found existing record: {existing_record_id}")
            else:
                self.logger.debug(f"   No existing record found for: {xml_id}")

        if existing_record_id:
            self.logger.debug(f"🔍 Step 4a: Updating existing record {xml_id}")
            # Update existing record
            if not noupdate:
                success = await self._update_security_record(model, existing_record_id, processed_values)
                if success:
                    self.logger.debug(f"Updated security record {xml_id}")
                    return True
            else:
                self.logger.debug(f"Skipped updating security record {xml_id} (noupdate=True)")
                return True
        else:
            self.logger.debug(f"🔍 Step 4b: Creating new record for {xml_id}")
            self.logger.debug(f"   Processed values: {processed_values}")

            # Create new record
            new_record_id = await self._create_security_record(model, processed_values)
            if new_record_id:
                self.logger.debug(f"🔍 Step 5: Storing XML ID mapping for {xml_id}")
                # Store XML ID mapping
                if xml_id:
                    await self._store_xmlid_mapping(xml_id, model, new_record_id)

                self.logger.debug(f"Created security record {xml_id or 'no-id'}")
                return True
            else:
                self.logger.error(f"❌ Failed to create security record for {xml_id}")

        return False
    
    async def _validate_security_data(self, values: Dict[str, Any], model: str) -> Dict[str, Any]:
        """Validate security data structure"""
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        if model == 'ir.model.access':
            # Validate access control list
            required_fields = ['name', 'model_id', 'group_id']
            for field in required_fields:
                if field not in values:
                    result['errors'].append(f"Missing required field for access control: {field}")
                    result['valid'] = False
            
            # Check permission fields
            permission_fields = ['perm_read', 'perm_write', 'perm_create', 'perm_unlink']
            has_permissions = any(field in values for field in permission_fields)
            if not has_permissions:
                result['warnings'].append("No permissions specified for access control")
        
        elif model == 'ir.rule':
            # Validate record rules
            required_fields = ['name', 'model_id']
            for field in required_fields:
                if field not in values:
                    result['errors'].append(f"Missing required field for record rule: {field}")
                    result['valid'] = False
            
            # Check domain filter
            if 'domain_force' not in values:
                result['warnings'].append("No domain filter specified for record rule")
        
        elif model == 'res.groups':
            # Validate groups
            if 'name' not in values:
                result['errors'].append("Missing required field for group: name")
                result['valid'] = False
        
        elif model == 'res.users':
            # Validate users
            required_fields = ['login', 'name']
            for field in required_fields:
                if field not in values:
                    result['errors'].append(f"Missing required field for user: {field}")
                    result['valid'] = False
        
        return result
    
    async def _process_security_values(self, values: Dict[str, Any], model: str) -> Optional[Dict[str, Any]]:
        """Process security-specific field values"""
        processed = {}

        self.logger.debug(f"🔧 Processing {len(values)} fields for security values")

        for field_name, field_def in values.items():
            self.logger.debug(f"   🔧 Processing field: {field_name} = {field_def}")
            try:
                field_value = None
                if field_name in ['model_id', 'group_id']:
                    # Special handling for model and group references
                    self.logger.debug(f"   🔗 Processing reference field: {field_name}")
                    field_value = await self._process_reference_field(field_def)
                elif field_name.startswith('perm_'):
                    # Special handling for permission fields (boolean)
                    self.logger.debug(f"   🔐 Processing permission field: {field_name}")
                    field_value = await self._process_permission_field(field_def)
                elif field_name == 'domain_force':
                    # Special handling for domain filter
                    self.logger.debug(f"   🎯 Processing domain field: {field_name}")
                    field_value = await self._process_domain_field(field_def)
                elif field_name in ['groups_id', 'users']:
                    # Special handling for Many2many fields
                    self.logger.debug(f"   🔗 Processing many2many field: {field_name}")
                    field_value = await self._process_many2many_field(field_def)
                else:
                    # Standard field processing
                    self.logger.debug(f"   📝 Processing standard field: {field_name}")
                    field_value = await self._process_standard_field(field_def)

                # Only add field to processed values if it's not None (skip unresolved references)
                if field_value is not None:
                    processed[field_name] = field_value
                    self.logger.debug(f"   ✅ Completed field: {field_name} -> {field_value}")
                else:
                    self.logger.debug(f"   ⏭️ Skipped field: {field_name} (unresolved reference)")

            except Exception as e:
                error_msg = f"Error processing security field {field_name}: {e}"
                self.logger.error(f"   ❌ {error_msg}")
                self.result.add_error(error_msg)
                return None

        self.logger.debug(f"🔧 Completed processing all fields")
        return processed
    
    async def _process_reference_field(self, field_def: Any) -> Optional[str]:
        """Process reference field (model_id, group_id, etc.)"""
        if isinstance(field_def, dict):
            if field_def.get('type') == 'ref':
                ref_value = field_def.get('value')
                resolved_ref = await self._resolve_reference(ref_value)
                if resolved_ref is None:
                    # If reference cannot be resolved, skip this field for now
                    # This allows records to be created even if dependencies don't exist yet
                    self.logger.warning(f"⚠️ Skipping unresolved reference field: {ref_value}")
                    return None
                return resolved_ref
            else:
                return field_def.get('value')
        else:
            return str(field_def) if field_def else None
    
    async def _process_permission_field(self, field_def: Any) -> bool:
        """Process permission field (boolean)"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value')
            
            if field_type == 'eval':
                return self._evaluate_boolean_expression(field_value)
            else:
                return self._convert_to_boolean(field_value)
        else:
            return self._convert_to_boolean(field_def)
    
    async def _process_domain_field(self, field_def: Any) -> str:
        """Process domain field (should be string)"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value', '')
            
            if field_type == 'eval':
                # For eval, return the expression as string
                return field_value
            else:
                return str(field_value)
        else:
            return str(field_def) if field_def else ''
    
    async def _process_many2many_field(self, field_def: Any) -> Optional[str]:
        """Process Many2many field"""
        if isinstance(field_def, dict):
            if field_def.get('type') == 'ref':
                ref_value = field_def.get('value')
                # Handle comma-separated references
                if ',' in ref_value:
                    refs = [ref.strip() for ref in ref_value.split(',')]
                    resolved_ids = []
                    for ref in refs:
                        ref_id = await self._resolve_reference(ref)
                        if ref_id:
                            resolved_ids.append(ref_id)
                    return ','.join(resolved_ids) if resolved_ids else None
                else:
                    return await self._resolve_reference(ref_value)
            else:
                return field_def.get('value')
        else:
            return str(field_def) if field_def else None
    
    async def _process_standard_field(self, field_def: Any) -> Any:
        """Process standard field values"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value')

            if field_type == 'ref':
                resolved_ref = await self._resolve_reference(field_value)
                # If reference cannot be resolved, return None to skip this field
                if resolved_ref is None:
                    self.logger.warning(f"⚠️ Skipping unresolved reference in standard field: {field_value}")
                    return None
                return resolved_ref
            elif field_type == 'eval':
                return self._evaluate_expression(field_value)
            else:
                return field_value
        else:
            return field_def
    
    def _convert_to_boolean(self, value: Any) -> bool:
        """Convert value to boolean"""
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        elif isinstance(value, (int, float)):
            return bool(value)
        else:
            return False
    
    def _evaluate_boolean_expression(self, expression: str) -> bool:
        """Evaluate boolean expression"""
        try:
            if expression.lower() in ('true', '1'):
                return True
            elif expression.lower() in ('false', '0'):
                return False
            else:
                return False
        except Exception:
            return False
    
    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a reference to another record"""
        self.logger.debug(f"🔍 Resolving reference: {ref_value}")
        try:
            # Add timeout to prevent hanging
            import asyncio
            result = await asyncio.wait_for(
                self.xmlid_manager.resolve_xmlid_to_record_id(
                    ref_value,
                    context=f"security rule reference in {self.context.get('current_model', 'unknown model')}"
                ),
                timeout=5.0  # 5 second timeout
            )
            if result:
                self.logger.debug(f"✅ Resolved reference {ref_value} -> {result}")
            else:
                self.logger.warning(f"⚠️ Reference {ref_value} not found")
            return result
        except asyncio.TimeoutError:
            warning_msg = f"⏰ Timeout resolving reference {ref_value} after 5 seconds"
            self.logger.warning(warning_msg)
            self.result.add_warning(warning_msg)
            return None
        except Exception as e:
            warning_msg = f"❌ Failed to resolve reference {ref_value}: {e}"
            self.logger.warning(warning_msg)
            self.result.add_warning(warning_msg)
            return None
    
    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        try:
            if expression == 'True':
                return True
            elif expression == 'False':
                return False
            elif expression == 'None':
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]
            elif 'ref(' in expression:
                # Handle expressions with ref() calls - for now, return None to skip
                # This prevents hanging on unresolved references in eval expressions
                self.logger.warning(f"⚠️ Skipping eval expression with ref() call: {expression}")
                return None
            else:
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression
        except Exception:
            return expression
    
    async def _find_record_by_xmlid(self, xml_id: str) -> Optional[str]:
        """Find a record ID by its XML ID"""
        try:
            self.logger.debug(f"🔍 Looking up XML ID: {xml_id}")
            result = await self.xmlid_manager.resolve_xmlid_to_record_id(xml_id)
            if result:
                self.logger.debug(f"✅ Found record ID: {result}")
            else:
                self.logger.debug(f"❌ No record found for XML ID: {xml_id}")
            return result
        except Exception as e:
            self.logger.debug(f"❌ Error looking up XML ID {xml_id}: {e}")
            return None
    
    async def _create_security_record(self, model: str, values: Dict[str, Any]) -> Optional[str]:
        """Create a new security record"""
        try:
            self.logger.debug(f"🔧 Creating record in model {model} with values: {values}")
            record_id = await self.model_sql.create_record(model, values)
            if record_id:
                self.logger.debug(f"✅ Successfully created record with ID: {record_id}")
            else:
                self.logger.error(f"❌ Failed to create record - no ID returned")
            return record_id
        except Exception as e:
            error_msg = f"Failed to create security record: {e}"
            self.logger.error(f"❌ {error_msg}")
            self.result.add_error(error_msg)
            return None
    
    async def _update_security_record(self, model: str, record_id: str, values: Dict[str, Any]) -> bool:
        """Update an existing security record"""
        try:
            return await self.model_sql.update_record(model, record_id, values)
        except Exception as e:
            self.result.add_error(f"Failed to update security record {record_id}: {e}")
            return False
    
    async def _store_xmlid_mapping(self, xml_id: str, model: str, record_id: str):
        """Store XML ID to record ID mapping"""
        try:
            if '.' in xml_id:
                module, name = xml_id.split('.', 1)
            else:
                module = self.context.get('addon_name', 'base')
                name = xml_id
            
            await self.xmlid_manager.create_xmlid_mapping(
                module=module,
                name=name,
                model=model,
                res_id=record_id
            )
        except Exception as e:
            self.result.add_warning(f"Failed to store XML ID mapping for {xml_id}: {e}")
